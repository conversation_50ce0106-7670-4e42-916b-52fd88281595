#!/usr/bin/env python3
"""
Test script to verify that pivot point calculation types (DAILY, WEEKLY, MONTHLY) work correctly.
"""

import logging
from config_loader import ConfigLoader
from pivot_point_integration import PivotPointIntegration
from fyers_client import FyersClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pivot_calculation_types():
    """Test all three calculation types."""
    
    # Test each calculation type
    calculation_types = ['DAILY', 'WEEKLY', 'MONTHLY']
    
    for calc_type in calculation_types:
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing {calc_type} pivot point calculation")
        logger.info(f"{'='*50}")
        
        try:
            # Load config
            config = ConfigLoader()
            
            # Temporarily override the calculation type for testing
            original_calc_type = config.pivot_point_calculation_type
            config._config['pivot_point_indicator']['calculation_type'] = calc_type
            
            # Initialize Fyers client
            fyers_client = FyersClient(config)
            
            # Authenticate
            if not fyers_client.authenticate():
                logger.error(f"Failed to authenticate Fyers client for {calc_type} test")
                continue
            
            # Initialize pivot point integration
            pivot_integration = PivotPointIntegration(config, fyers_client)
            
            # Verify the calculation type is set correctly
            assert pivot_integration.calculation_type == calc_type, f"Expected {calc_type}, got {pivot_integration.calculation_type}"
            logger.info(f"✓ Pivot integration initialized with calculation_type: {pivot_integration.calculation_type}")
            
            # Test with a sample symbol (you can modify this based on your needs)
            test_symbol = "NSE:NIFTY-INDEX"  # Using index symbol for testing
            
            # Create a mock market data object
            class MockMarketData:
                def __init__(self):
                    self.ltp = 24500.0  # Sample LTP for NIFTY
            
            mock_market_data = MockMarketData()
            
            # Test pivot point calculation
            pivot_data = pivot_integration.calculate_pivot_points_for_symbol(test_symbol, mock_market_data)
            
            if pivot_data:
                logger.info(f"✓ {calc_type} pivot points calculated successfully")
                logger.info(f"  Sample pivot levels: Pivot={pivot_data.pivot_levels.get('Pivot', 'N/A')}, R1={pivot_data.pivot_levels.get('R1', 'N/A')}, S1={pivot_data.pivot_levels.get('S1', 'N/A')}")
            else:
                logger.warning(f"⚠ {calc_type} pivot point calculation returned None (this might be expected if no data is available)")
            
            # Restore original calculation type
            config._config['pivot_point_indicator']['calculation_type'] = original_calc_type
            
        except Exception as e:
            logger.error(f"✗ Error testing {calc_type} calculation: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    logger.info("Starting pivot point calculation type tests...")
    test_pivot_calculation_types()
    logger.info("Test completed!")