import logging
from config_loader import get_config
from fyers_config import FyersConfig
from fyers_connect import FyersConnect

def test_fetch_intraday_data():
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Load config and initialize FyersConnect
    config_loader = get_config()
    config = config_loader._config  # Fix: get the actual config dictionary
    fyers_config = FyersConfig(config['general']['env_path'])
    fyers = FyersConnect(fyers_config)
    
    # Login
    if not fyers.login():
        print("Login failed.")
        return

    symbol = "NIFTY"  # You can change this to any symbol in your config

    from datetime import datetime
    # Assume market start time is 09:15
    market_start = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
    now = datetime.now()
    start_date = market_start.strftime('%Y-%m-%d %H:%M')
    end_date = now.strftime('%Y-%m-%d %H:%M')

    import os
    ohlc_dir = os.path.join(os.getcwd(), "OHLC")
    os.makedirs(ohlc_dir, exist_ok=True)
    for interval in ["1", "5", "15", "30", "60"]:
        print(f"\nFetching OHLC data for {symbol} from {start_date} to {end_date} (interval: {interval} mins):")
        df = fyers.get_ohlc_data(symbol, interval=interval, start_date=market_start.strftime('%Y-%m-%d'), end_date=now.strftime('%Y-%m-%d'))
        print(df)  # Show all rows for today
        # Convert index from UTC to Asia/Kolkata and remove timezone info
        if not df.empty:
            import pytz
            df.index = df.index.tz_localize('UTC').tz_convert('Asia/Kolkata').tz_localize(None)
        # Save to CSV
        csv_path = os.path.join(ohlc_dir, f"{symbol}_OHLC_{interval}min.csv")
        df.to_csv(csv_path)
        print(f"Saved {interval} min OHLC data to {csv_path}")

if __name__ == "__main__":
    test_fetch_intraday_data()