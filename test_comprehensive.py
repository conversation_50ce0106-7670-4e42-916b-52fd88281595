"""
Comprehensive end-to-end test script for the Multi-Market Scanner.
Tests all market types, configurations, and functionality.
"""

import os
import sys
import logging
import tempfile
import shutil
from datetime import datetime
from typing import List, Dict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader
from unified_scanner import UnifiedScanner
from universal_symbol_parser import UniversalSymbolParser
from options_chain_filter import OptionsChainFilter
from market_type_scanner import MarketTypeScannerFactory

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveTestSuite:
    """Comprehensive test suite for the multi-market scanner."""
    
    def __init__(self):
        self.test_results = {}
        self.temp_dir = None
        
    def setup_test_environment(self):
        """Setup test environment."""
        logger.info("Setting up test environment...")
        
        # Change to script directory
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp(prefix="scanner_test_")
        logger.info(f"Created temporary test directory: {self.temp_dir}")
        
    def cleanup_test_environment(self):
        """Cleanup test environment."""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info("Cleaned up test environment")
    
    def test_configuration_loading(self):
        """Test configuration loading for different scenarios."""
        logger.info("Testing configuration loading...")
        
        test_configs = [
            'test_config_all_markets.yaml',
            'test_config_options_only.yaml',
            'config.yaml'
        ]
        
        for config_file in test_configs:
            try:
                if os.path.exists(config_file):
                    config = ConfigLoader(config_file)
                    market_types = config.get_enabled_market_types()
                    logger.info(f"✓ {config_file}: {len(market_types)} market types enabled")
                    self.test_results[f"config_{config_file}"] = True
                else:
                    logger.warning(f"Config file not found: {config_file}")
                    self.test_results[f"config_{config_file}"] = False
            except Exception as e:
                logger.error(f"✗ Error loading {config_file}: {e}")
                self.test_results[f"config_{config_file}"] = False
    
    def test_symbol_parsing(self):
        """Test universal symbol parsing for all market types."""
        logger.info("Testing universal symbol parsing...")
        
        try:
            config = ConfigLoader('test_config_all_markets.yaml')
            target_symbols = config.get_target_symbols()
            parser = UniversalSymbolParser(config, target_symbols)
            
            # Test parsing for each market type
            test_symbols = {
                'EQUITY': 'NSE:RELIANCE-EQ',
                'INDEX': 'NSE:NIFTY-INDEX',
                'FUTURES': 'NSE:NIFTY25JANFUT',
                'OPTIONS': 'NSE:NIFTY25JUL24500CE'
            }
            
            for market_type, symbol in test_symbols.items():
                try:
                    csv_file = config.get_csv_file_for_market_type(market_type)
                    if os.path.exists(csv_file):
                        parsed = parser.parse_symbol(symbol, csv_file)
                        if parsed and parsed.market_type == market_type:
                            logger.info(f"✓ {market_type} parsing successful")
                            self.test_results[f"parsing_{market_type}"] = True
                        else:
                            logger.warning(f"✗ {market_type} parsing failed")
                            self.test_results[f"parsing_{market_type}"] = False
                    else:
                        logger.warning(f"CSV file not found for {market_type}")
                        self.test_results[f"parsing_{market_type}"] = False
                except Exception as e:
                    logger.error(f"✗ Error parsing {market_type}: {e}")
                    self.test_results[f"parsing_{market_type}"] = False
                    
        except Exception as e:
            logger.error(f"Symbol parsing test failed: {e}")
    
    def test_options_filtering(self):
        """Test intelligent options filtering."""
        logger.info("Testing options filtering...")
        
        try:
            config = ConfigLoader('test_config_options_only.yaml')
            options_filter = OptionsChainFilter(config)
            
            # Test strike interval detection
            intervals = {
                'NIFTY': 50,
                'BANKNIFTY': 100,
                'FINNIFTY': 50
            }
            
            for underlying, expected_interval in intervals.items():
                actual_interval = options_filter.get_strike_interval(underlying)
                if actual_interval == expected_interval:
                    logger.info(f"✓ Strike interval for {underlying}: {actual_interval}")
                    self.test_results[f"strike_interval_{underlying}"] = True
                else:
                    logger.warning(f"✗ Strike interval mismatch for {underlying}: expected {expected_interval}, got {actual_interval}")
                    self.test_results[f"strike_interval_{underlying}"] = False
            
            # Test dynamic strike range calculation
            test_spot_price = 24500.0
            min_strike, max_strike = options_filter.calculate_dynamic_strike_range('NIFTY', test_spot_price)
            logger.info(f"✓ Dynamic strike range for NIFTY (spot: {test_spot_price}): {min_strike} - {max_strike}")
            self.test_results["dynamic_strike_range"] = True
            
        except Exception as e:
            logger.error(f"Options filtering test failed: {e}")
            self.test_results["options_filtering"] = False
    
    def test_market_scanners(self):
        """Test market scanners for all market types."""
        logger.info("Testing market scanners...")
        
        try:
            config = ConfigLoader('test_config_all_markets.yaml')
            
            for market_type in config.get_enabled_market_types():
                try:
                    scanner = MarketTypeScannerFactory.create_scanner(market_type, config)
                    symbols = scanner.get_symbols_for_scanning(['NIFTY', 'RELIANCE'])
                    logger.info(f"✓ {market_type} scanner: loaded {len(symbols)} symbols")
                    self.test_results[f"scanner_{market_type}"] = True
                except Exception as e:
                    logger.error(f"✗ {market_type} scanner failed: {e}")
                    self.test_results[f"scanner_{market_type}"] = False
                    
        except Exception as e:
            logger.error(f"Market scanner test failed: {e}")
    
    def test_unified_scanner_dry_run(self):
        """Test unified scanner without API calls."""
        logger.info("Testing unified scanner (dry run)...")
        
        try:
            scanner = UnifiedScanner('test_config_all_markets.yaml')
            logger.info(f"✓ Unified scanner initialized for: {scanner.enabled_market_types}")
            
            # Test prerequisites validation
            prereq_result = scanner.validate_prerequisites()
            logger.info(f"✓ Prerequisites validation: {prereq_result}")
            
            self.test_results["unified_scanner_init"] = True
            
        except Exception as e:
            logger.error(f"Unified scanner test failed: {e}")
            self.test_results["unified_scanner_init"] = False
    
    def run_all_tests(self):
        """Run all tests in the suite."""
        logger.info("=" * 80)
        logger.info("STARTING COMPREHENSIVE TEST SUITE")
        logger.info("=" * 80)
        
        try:
            self.setup_test_environment()
            
            # Run individual tests
            self.test_configuration_loading()
            self.test_symbol_parsing()
            self.test_options_filtering()
            self.test_market_scanners()
            self.test_unified_scanner_dry_run()
            
            # Print results summary
            self.print_test_summary()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return False
        finally:
            self.cleanup_test_environment()
        
        return True
    
    def print_test_summary(self):
        """Print comprehensive test results summary."""
        logger.info("=" * 80)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("=" * 80)
        
        passed = sum(1 for result in self.test_results.values() if result)
        total = len(self.test_results)
        
        logger.info(f"Tests Passed: {passed}/{total}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        logger.info("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "✓ PASS" if result else "✗ FAIL"
            logger.info(f"  {test_name}: {status}")
        
        if passed == total:
            logger.info("\n🎉 ALL TESTS PASSED! The scanner is ready for production use.")
        else:
            logger.warning(f"\n⚠️  {total - passed} tests failed. Please review the issues above.")

def main():
    """Main test function."""
    test_suite = ComprehensiveTestSuite()
    success = test_suite.run_all_tests()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
